Write-Host "启动中文版 Keycloak..." -ForegroundColor Green

# 数据库配置
$env:KC_DB = "mysql"
$env:KC_DB_USERNAME = "root"
$env:KC_DB_PASSWORD = "xxx0501"
$env:KC_DB_URL = "***********************************************************************************************************"

# 全局中文设置
$env:KC_SPI_THEME_DEFAULT_LOCALE = "zh-CN"
$env:KC_SPI_THEME_ADMIN_DEFAULT_LOCALE = "zh-CN"

# 强制启用国际化
$env:KC_SPI_THEME_DEFAULT_INTERNATIONALIZATION_ENABLED = "true"
$env:KC_SPI_THEME_DEFAULT_SUPPORTED_LOCALES = "zh-CN,en"

Write-Host "配置已设置，正在启动 Keycloak..." -ForegroundColor Yellow

# 启动 Keycloak
& ".\bin\kc.bat" start-dev --http-port=7002 --bootstrap-admin-username=admin --bootstrap-admin-password=admin

Write-Host "Keycloak 已启动在端口 7002" -ForegroundColor Green
Write-Host "管理控制台: http://localhost:7002/admin" -ForegroundColor Cyan
Write-Host "用户名/密码: admin/admin" -ForegroundColor Cyan
